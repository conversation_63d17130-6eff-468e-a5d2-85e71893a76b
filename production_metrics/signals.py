"""
Django signals for auto-calculating production metrics
"""
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.exceptions import ObjectDoesNotExist
import logging

from .models import ProductionSchedule, ProductionMetrics, LineCapacity
from .services.calculator import ProductionMetricsCalculator

logger = logging.getLogger(__name__)


@receiver(post_save, sender=ProductionSchedule)
def calculate_production_metrics(sender, instance, created, **kwargs):
    """
    Auto-calculate production metrics when a ProductionSchedule is saved
    """
    # Only calculate if actual_quantity is provided
    if instance.actual_quantity is None:
        logger.debug(f"Skipping metrics calculation for {instance} - no actual quantity")
        return

    try:
        # Get the line capacity configuration for this line-product combination
        # First, get the product from the work order
        work_order = instance.work_order
        if not hasattr(work_order, 'product') or not work_order.product:
            logger.warning(f"No product found for work order {work_order}")
            return

        line_capacity = LineCapacity.objects.get(
            line=instance.line,
            product=work_order.product,
            is_active=True
        )

        # Calculate all metrics using the calculator service
        metrics_data = ProductionMetricsCalculator.calculate_all_metrics(
            total_time_minutes=instance.total_time_minutes,
            planned_break_minutes=instance.planned_break_minutes,
            downtime_minutes=instance.downtime_minutes,
            uph=line_capacity.uph,
            manpower=instance.manpower,
            actual_quantity=instance.actual_quantity,
            rejection_quantity=instance.rejection_quantity,
            bottleneck_time_seconds=line_capacity.bottleneck_time_seconds
        )

        # Create or update the ProductionMetrics record
        metrics, created = ProductionMetrics.objects.update_or_create(
            schedule=instance,
            defaults={
                'line_capacity': line_capacity,
                'machine_runtime_hours': metrics_data['machine_runtime_hours'],
                'target_quantity': metrics_data['target_quantity'],
                'actual_ok_quantity': metrics_data['actual_ok_quantity'],
                'efficiency_percentage': metrics_data['efficiency_percentage'],
                'quality_percentage': metrics_data['quality_percentage'],
                'upph_actual': metrics_data['upph_actual'],
                'line_capacity_calculated': metrics_data['line_capacity_calculated'],
            }
        )

        action = "Created" if created else "Updated"
        logger.info(f"{action} production metrics for {instance}")

    except LineCapacity.DoesNotExist:
        logger.error(
            f"No line capacity configuration found for line {instance.line} "
            f"and product {work_order.product if hasattr(work_order, 'product') else 'Unknown'}"
        )
    except Exception as e:
        logger.error(f"Error calculating production metrics for {instance}: {str(e)}")


@receiver(post_delete, sender=ProductionSchedule)
def delete_production_metrics(sender, instance, **kwargs):
    """
    Clean up ProductionMetrics when a ProductionSchedule is deleted
    """
    try:
        # The metrics should be automatically deleted due to CASCADE relationship
        # This signal is mainly for logging purposes
        logger.info(f"Production metrics deleted for schedule {instance}")
    except Exception as e:
        logger.error(f"Error during metrics cleanup for {instance}: {str(e)}")


# @receiver(post_save, sender=LineCapacity)
# def recalculate_metrics_on_capacity_change(sender, instance, created, **kwargs):
#     """
#     Recalculate metrics for all schedules when line capacity configuration changes
#     """
#     if created:
#         logger.info(f"New line capacity created: {instance}")
#         return

#     try:
#         # Find all production schedules that use this line capacity
#         # and have actual quantities (need recalculation)
#         schedules_to_recalculate = ProductionSchedule.objects.filter(
#             line=instance.line,
#             work_order__product=instance.product,
#             actual_quantity__isnull=False
#         ).select_related('work_order')

#         recalculated_count = 0
#         for schedule in schedules_to_recalculate:
#             # Trigger recalculation by calling the signal manually
#             calculate_production_metrics(
#                 sender=ProductionSchedule,
#                 instance=schedule,
#                 created=False
#             )
#             recalculated_count += 1

#         if recalculated_count > 0:
#             logger.info(
#                 f"Recalculated metrics for {recalculated_count} schedules "
#                 f"due to capacity change for {instance}"
#             )

#     except Exception as e:
#         logger.error(f"Error recalculating metrics after capacity change {instance}: {str(e)}")
