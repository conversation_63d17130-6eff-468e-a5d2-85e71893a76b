from rest_framework import viewsets, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from datetime import datetime

from .models import Shift, LineCapacity, ProductionSchedule, ProductionMetrics, DowntimeLog
from .serializers import (
    ShiftSerializer, LineCapacitySerializer, ProductionScheduleSerializer,
    ProductionMetricsSerializer, DowntimeLogSerializer
)


class ShiftViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Shift objects

    Provides CRUD operations for production shifts with filtering capabilities.
    """
    queryset = Shift.objects.all()
    serializer_class = ShiftSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'duration_hours']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'start_time', 'end_time', 'duration_hours', 'created_at']
    ordering = ['start_time']

    @action(detail=False, methods=['get'])
    def by_time_range(self, request):
        """
        Filter shifts by start and end time query parameters

        Query parameters:
        - start_time: Filter shifts that start at or after this time (HH:MM format)
        - end_time: Filter shifts that end at or before this time (HH:MM format)
        """
        start_time_str = request.query_params.get('start_time')
        end_time_str = request.query_params.get('end_time')

        queryset = self.get_queryset()

        if start_time_str:
            try:
                start_time = datetime.strptime(start_time_str, '%H:%M').time()
                queryset = queryset.filter(start_time__gte=start_time)
            except ValueError:
                return Response(
                    {'error': 'Invalid start_time format. Use HH:MM format.'},
                    status=400
                )

        if end_time_str:
            try:
                end_time = datetime.strptime(end_time_str, '%H:%M').time()
                queryset = queryset.filter(end_time__lte=end_time)
            except ValueError:
                return Response(
                    {'error': 'Invalid end_time format. Use HH:MM format.'},
                    status=400
                )

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class LineCapacityViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing LineCapacity objects

    Provides CRUD operations for line capacity configurations.
    """
    queryset = LineCapacity.objects.select_related('line', 'product').all()
    serializer_class = LineCapacitySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['line', 'product', 'is_active', 'line__area']
    search_fields = ['line__name', 'product__name', 'product__code']
    ordering_fields = ['line__name', 'product__name', 'uph', 'manpower', 'created_at']
    ordering = ['line__name', 'product__name']

    def get_queryset(self):
        """Optimize queryset with select_related"""
        return super().get_queryset().select_related('line', 'product')


class ProductionScheduleViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing ProductionSchedule objects

    Provides CRUD operations for production schedules with comprehensive filtering.
    """
    queryset = ProductionSchedule.objects.select_related(
        'line', 'work_order', 'shift', 'created_by'
    ).prefetch_related('metrics').all()
    serializer_class = ProductionScheduleSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'production_date', 'line', 'work_order', 'shift', 'created_by'
    ]
    search_fields = [
        'work_order__order_no', 'work_order__part_no', 'work_order__customer',
        'line__name', 'downtime_reason'
    ]
    ordering_fields = [
        'production_date', 'start_time', 'line__name', 'actual_quantity', 'created_at'
    ]
    ordering = ['-production_date', 'start_time', 'line__name']

    def get_queryset(self):
        """Optimize queryset with select_related and prefetch_related"""
        return super().get_queryset().select_related(
            'line', 'work_order', 'shift', 'created_by'
        ).prefetch_related('metrics')

    def perform_create(self, serializer):
        """Set the created_by field when creating a new schedule"""
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def by_date_range(self, request):
        """
        Filter schedules by date range

        Query parameters:
        - start_date: Start date (YYYY-MM-DD format)
        - end_date: End date (YYYY-MM-DD format)
        """
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        queryset = self.get_queryset()

        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(production_date__gte=start_date)
            except ValueError:
                return Response(
                    {'error': 'Invalid start_date format. Use YYYY-MM-DD format.'},
                    status=400
                )

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(production_date__lte=end_date)
            except ValueError:
                return Response(
                    {'error': 'Invalid end_date format. Use YYYY-MM-DD format.'},
                    status=400
                )

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class ProductionMetricsViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing ProductionMetrics objects (read-only)

    Production metrics are auto-calculated and cannot be directly modified.
    """
    queryset = ProductionMetrics.objects.select_related(
        'schedule__line', 'schedule__work_order', 'line_capacity__product'
    ).all()
    serializer_class = ProductionMetricsSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'schedule__production_date', 'schedule__line', 'schedule__work_order',
        'line_capacity__product'
    ]
    search_fields = [
        'schedule__work_order__order_no', 'schedule__work_order__customer',
        'schedule__line__name', 'line_capacity__product__name'
    ]
    ordering_fields = [
        'schedule__production_date', 'efficiency_percentage', 'quality_percentage',
        'target_quantity', 'actual_ok_quantity', 'created_at'
    ]
    ordering = ['-schedule__production_date', 'schedule__start_time']

    def get_queryset(self):
        """Optimize queryset with select_related"""
        return super().get_queryset().select_related(
            'schedule__line', 'schedule__work_order', 'schedule__shift',
            'line_capacity__line', 'line_capacity__product'
        )

    @action(detail=False, methods=['get'])
    def efficiency_above(self, request):
        """Get metrics with efficiency above specified threshold"""
        threshold = request.query_params.get('threshold', 100)
        try:
            threshold = float(threshold)
            queryset = self.get_queryset().filter(efficiency_percentage__gte=threshold)

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except ValueError:
            return Response(
                {'error': 'Invalid threshold value. Must be a number.'},
                status=400
            )

    @action(detail=False, methods=['get'])
    def quality_below(self, request):
        """Get metrics with quality below specified threshold"""
        threshold = request.query_params.get('threshold', 95)
        try:
            threshold = float(threshold)
            queryset = self.get_queryset().filter(quality_percentage__lt=threshold)

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except ValueError:
            return Response(
                {'error': 'Invalid threshold value. Must be a number.'},
                status=400
            )


class DowntimeLogViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing DowntimeLog objects

    Provides CRUD operations for downtime tracking.
    """
    queryset = DowntimeLog.objects.select_related(
        'schedule__line', 'schedule__work_order', 'logged_by'
    ).all()
    serializer_class = DowntimeLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'downtime_type', 'reason', 'schedule__production_date',
        'schedule__line', 'logged_by'
    ]
    search_fields = [
        'reason', 'description', 'schedule__work_order__order_no',
        'schedule__line__name'
    ]
    ordering_fields = [
        'start_time', 'duration_minutes', 'schedule__production_date', 'created_at'
    ]
    ordering = ['-start_time']

    def get_queryset(self):
        """Optimize queryset with select_related"""
        return super().get_queryset().select_related(
            'schedule__line', 'schedule__work_order', 'logged_by'
        )

    def perform_create(self, serializer):
        """Set the logged_by field when creating a new downtime log"""
        serializer.save(logged_by=self.request.user)

    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """Filter downtime logs by type"""
        downtime_type = request.query_params.get('type')
        if not downtime_type:
            return Response(
                {'error': 'type parameter is required'},
                status=400
            )

        if downtime_type not in ['planned', 'unplanned']:
            return Response(
                {'error': 'type must be either "planned" or "unplanned"'},
                status=400
            )

        queryset = self.get_queryset().filter(downtime_type=downtime_type)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def duration_above(self, request):
        """Get downtime logs with duration above specified minutes"""
        threshold = request.query_params.get('threshold', 30)
        try:
            threshold = int(threshold)
            queryset = self.get_queryset().filter(duration_minutes__gte=threshold)

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except ValueError:
            return Response(
                {'error': 'Invalid threshold value. Must be an integer.'},
                status=400
            )
