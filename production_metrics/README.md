# Production Metrics App

This Django app manages production metrics, scheduling, and capacity tracking for the MES (Manufacturing Execution System).

## Overview

The Production Metrics app implements the data structure and calculations defined in the Production Metrics Data Dictionary. It provides comprehensive tracking of production efficiency, quality, and capacity utilization across manufacturing lines.

## Models

### Shift
Defines shift configurations for production scheduling.
- **Fields**: name, code, start_time, end_time, duration_hours
- **Purpose**: Configure work shifts for production planning

### LineCapacity
Stores capacity configuration for each line-product combination.
- **Fields**: line, product, uph, manpower, upph, standard_time_seconds, bottleneck_time_seconds
- **Purpose**: Define production capacity parameters for calculations
- **Unique Constraint**: (line, product)

### ProductionSchedule
Tracks hourly production schedules with manual inputs.
- **Fields**: production_date, start_time, end_time, line, work_order, shift, manpower, actual_quantity, etc.
- **Purpose**: Record actual production data for each hour
- **Unique Constraint**: (production_date, start_time, line)

### ProductionMetrics
Auto-calculated production metrics based on schedule data.
- **Fields**: target_quantity, efficiency_percentage, quality_percentage, upph_actual, etc.
- **Purpose**: Store calculated KPIs and performance metrics
- **Relationship**: OneToOne with ProductionSchedule

### DowntimeLog
Detailed tracking of production downtime events.
- **Fields**: downtime_type, duration_minutes, reason, description, start_time, end_time
- **Purpose**: Track and categorize production interruptions

## Key Features

### Auto-Calculation Engine
- Automatic calculation of production metrics when schedule data is saved
- Implements formulas from the production metrics data dictionary
- Triggered via Django signals

### Business Rules Validation
- Time range validation (start < end time)
- Capacity constraints validation
- Data integrity checks (rejection ≤ actual quantity)

### Integration Points
- **catalog.Product**: Product definitions
- **operation.WorkOrder**: Work order management
- **workflow_config.AssemblyLine**: Production line configuration
- **authentication.User**: User tracking for data entry

## Services

### ProductionMetricsCalculator
Service class implementing calculation formulas:
- `calculate_machine_runtime_hours()`
- `calculate_target_quantity()`
- `calculate_efficiency_percentage()`
- `calculate_quality_percentage()`
- `calculate_upph_actual()`

## Database Tables

All tables follow the `mes_` prefix convention:
- `mes_production_shifts`
- `mes_line_capacity`
- `mes_production_schedule`
- `mes_production_metrics`
- `mes_downtime_log`

## ClickHouse Integration

Tables include `updated_at` fields for automatic replication to ClickHouse analytics database via PeerDB.

## Usage

1. **Configure Shifts**: Define work shifts in the admin interface
2. **Set Line Capacities**: Configure UPH, manpower, and time standards for each line-product combination
3. **Enter Production Data**: Record hourly production schedules with actual quantities
4. **View Metrics**: Automatically calculated metrics are available in ProductionMetrics model
5. **Track Downtime**: Log detailed downtime events for analysis

## Admin Interface

Comprehensive admin interface with:
- List views with filtering and searching
- Detailed fieldsets for data entry
- Calculated field displays
- Related object navigation

## REST API Endpoints

### Base URL: `/mes_trace/production-metrics/api/`

#### Shifts
- `GET /shifts/` - List all shifts
- `POST /shifts/` - Create new shift
- `GET /shifts/{id}/` - Get specific shift
- `PUT /shifts/{id}/` - Update shift
- `DELETE /shifts/{id}/` - Delete shift
- `GET /shifts/by_time_range/?start_time=HH:MM&end_time=HH:MM` - Filter by time range

#### Line Capacities
- `GET /line-capacities/` - List all line capacities
- `POST /line-capacities/` - Create new line capacity
- `GET /line-capacities/{id}/` - Get specific line capacity
- `PUT /line-capacities/{id}/` - Update line capacity
- `DELETE /line-capacities/{id}/` - Delete line capacity

#### Production Schedules
- `GET /schedules/` - List all production schedules
- `POST /schedules/` - Create new production schedule
- `GET /schedules/{id}/` - Get specific schedule
- `PUT /schedules/{id}/` - Update schedule
- `DELETE /schedules/{id}/` - Delete schedule
- `GET /schedules/by_date_range/?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD` - Filter by date range
- `GET /schedules/with_metrics/` - Get schedules with calculated metrics
- `GET /schedules/without_metrics/` - Get schedules without metrics

#### Production Metrics (Read-Only)
- `GET /metrics/` - List all production metrics
- `GET /metrics/{id}/` - Get specific metrics
- `GET /metrics/efficiency_above/?threshold=100` - Get metrics with efficiency above threshold
- `GET /metrics/quality_below/?threshold=95` - Get metrics with quality below threshold

#### Downtime Logs
- `GET /downtime-logs/` - List all downtime logs
- `POST /downtime-logs/` - Create new downtime log
- `GET /downtime-logs/{id}/` - Get specific downtime log
- `PUT /downtime-logs/{id}/` - Update downtime log
- `DELETE /downtime-logs/{id}/` - Delete downtime log
- `GET /downtime-logs/by_type/?type=planned|unplanned` - Filter by downtime type
- `GET /downtime-logs/duration_above/?threshold=30` - Get logs with duration above threshold

### API Features
- **Authentication Required**: All endpoints require authentication
- **Filtering & Search**: Django Filter backend with comprehensive filtering options
- **Pagination**: Automatic pagination for large datasets
- **Ordering**: Configurable ordering on multiple fields
- **Auto-Calculation**: Production metrics automatically calculated via Django signals
- **Validation**: Comprehensive validation including shift alignment checks

## Validation Features

### Shift Validation
- **Time Alignment**: Schedule times must align with selected shift (±15 minutes tolerance)
- **Midnight Crossing**: Supports shifts that cross midnight (e.g., 22:00 - 06:00)
- **Flexible Scheduling**: Allows schedules in either part of midnight-crossing shifts

### Business Rules
- Start time must be before end time
- Break and downtime cannot exceed total time
- Rejection quantity cannot exceed actual quantity
- UPPH calculation consistency validation
- Bottleneck time cannot exceed standard time

## Future Enhancements

- Bulk data import utilities
- Real-time dashboard integration
- Advanced analytics and reporting
- Excel export functionality
