# Production Metrics App

This Django app manages production metrics, scheduling, and capacity tracking for the MES (Manufacturing Execution System).

## Overview

The Production Metrics app implements the data structure and calculations defined in the Production Metrics Data Dictionary. It provides comprehensive tracking of production efficiency, quality, and capacity utilization across manufacturing lines.

## Models

### Shift
Defines shift configurations for production scheduling.
- **Fields**: name, code, start_time, end_time, duration_hours
- **Purpose**: Configure work shifts for production planning

### LineCapacity
Stores capacity configuration for each line-product combination.
- **Fields**: line, product, uph, manpower, upph, standard_time_seconds, bottleneck_time_seconds
- **Purpose**: Define production capacity parameters for calculations
- **Unique Constraint**: (line, product)

### ProductionSchedule
Tracks hourly production schedules with manual inputs.
- **Fields**: production_date, start_time, end_time, line, work_order, shift, manpower, actual_quantity, etc.
- **Purpose**: Record actual production data for each hour
- **Unique Constraint**: (production_date, start_time, line)

### ProductionMetrics
Auto-calculated production metrics based on schedule data.
- **Fields**: target_quantity, efficiency_percentage, quality_percentage, upph_actual, etc.
- **Purpose**: Store calculated KPIs and performance metrics
- **Relationship**: OneToOne with ProductionSchedule

### DowntimeLog
Detailed tracking of production downtime events.
- **Fields**: downtime_type, duration_minutes, reason, description, start_time, end_time
- **Purpose**: Track and categorize production interruptions

## Key Features

### Auto-Calculation Engine
- Automatic calculation of production metrics when schedule data is saved
- Implements formulas from the production metrics data dictionary
- Triggered via Django signals

### Business Rules Validation
- Time range validation (start < end time)
- Capacity constraints validation
- Data integrity checks (rejection ≤ actual quantity)

### Integration Points
- **catalog.Product**: Product definitions
- **operation.WorkOrder**: Work order management
- **workflow_config.AssemblyLine**: Production line configuration
- **authentication.User**: User tracking for data entry

## Services

### ProductionMetricsCalculator
Service class implementing calculation formulas:
- `calculate_machine_runtime_hours()`
- `calculate_target_quantity()`
- `calculate_efficiency_percentage()`
- `calculate_quality_percentage()`
- `calculate_upph_actual()`

## Database Tables

All tables follow the `mes_` prefix convention:
- `mes_production_shifts`
- `mes_line_capacity`
- `mes_production_schedule`
- `mes_production_metrics`
- `mes_downtime_log`

## ClickHouse Integration

Tables include `updated_at` fields for automatic replication to ClickHouse analytics database via PeerDB.

## Usage

1. **Configure Shifts**: Define work shifts in the admin interface
2. **Set Line Capacities**: Configure UPH, manpower, and time standards for each line-product combination
3. **Enter Production Data**: Record hourly production schedules with actual quantities
4. **View Metrics**: Automatically calculated metrics are available in ProductionMetrics model
5. **Track Downtime**: Log detailed downtime events for analysis

## Admin Interface

Comprehensive admin interface with:
- List views with filtering and searching
- Detailed fieldsets for data entry
- Calculated field displays
- Related object navigation

## Future Enhancements

- REST API endpoints for data entry and retrieval
- Bulk data import utilities
- Real-time dashboard integration
- Advanced analytics and reporting
