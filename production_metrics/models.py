from django.db import models
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator, MaxValueValidator
from datetime import datetime, date
from core.models import BaseModel, BaseEntity


class Shift(BaseEntity):
    """
    Shift configuration model for production scheduling
    """
    start_time = models.TimeField(help_text="Shift start time")
    end_time = models.TimeField(help_text="Shift end time")
    duration_hours = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        help_text="Shift duration in hours",
        validators=[MinValueValidator(0.1), MaxValueValidator(24.0)]
    )

    class Meta:
        db_table = 'mes_production_shifts'
        ordering = ['start_time']

    def __str__(self):
        return f"{self.name} ({self.start_time} - {self.end_time})"

    def clean(self):
        """Custom validation for shift times"""
        super().clean()
        if self.start_time and self.end_time:
            # Handle shifts that cross midnight
            if self.start_time >= self.end_time:
                # This is a shift that crosses midnight (e.g., 22:00 - 06:00)
                duration = (24 * 60 - (self.start_time.hour * 60 + self.start_time.minute) +
                           (self.end_time.hour * 60 + self.end_time.minute)) / 60
            else:
                # Normal shift within the same day
                start_minutes = self.start_time.hour * 60 + self.start_time.minute
                end_minutes = self.end_time.hour * 60 + self.end_time.minute
                duration = (end_minutes - start_minutes) / 60

            if abs(float(self.duration_hours) - duration) > 0.1:  # Allow small rounding differences
                raise ValidationError(
                    f"Duration hours ({self.duration_hours}) doesn't match calculated duration ({duration:.2f})"
                )


class LineCapacity(BaseModel):
    """
    Line capacity configuration per line-product combination
    """
    line = models.ForeignKey(
        'workflow_config.AssemblyLine',
        on_delete=models.PROTECT,
        related_name='capacities'
    )
    product = models.ForeignKey(
        'catalog.Product',
        on_delete=models.PROTECT,
        related_name='line_capacities'
    )
    uph = models.IntegerField(
        help_text="Units Per Hour",
        validators=[MinValueValidator(1)]
    )
    manpower = models.IntegerField(
        help_text="Required manpower",
        validators=[MinValueValidator(1)]
    )
    upph = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        help_text="Units Per Person Hour",
        validators=[MinValueValidator(0.01)]
    )
    standard_time_seconds = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Standard time per unit in seconds",
        validators=[MinValueValidator(0.01)]
    )
    bottleneck_time_seconds = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Bottleneck time per unit in seconds",
        validators=[MinValueValidator(0.01)]
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_line_capacity'
        unique_together = ['line', 'product']
        ordering = ['line__name', 'product__name']

    def __str__(self):
        return f"{self.line.name}.{self.product.name} - {self.uph} UPH"

    def clean(self):
        """Custom validation for capacity configuration"""
        super().clean()

        # Validate UPPH calculation
        if self.uph and self.manpower:
            calculated_upph = self.uph / self.manpower
            if abs(float(self.upph) - calculated_upph) > 0.01:
                raise ValidationError(
                    f"UPPH ({self.upph}) should equal UPH/Manpower ({calculated_upph:.2f})"
                )

        # Validate bottleneck time is not greater than standard time
        if (self.bottleneck_time_seconds and self.standard_time_seconds and
            self.bottleneck_time_seconds > self.standard_time_seconds):
            raise ValidationError(
                "Bottleneck time cannot be greater than standard time"
            )

    @property
    def capacity_id(self):
        """Generate capacity ID in Line.Product format for compatibility"""
        return f"{self.line.name}.{self.product.name}"


class ProductionSchedule(BaseModel):
    """
    Hourly production schedule with manual inputs
    """
    production_date = models.DateField(help_text="Production date")
    start_time = models.TimeField(help_text="Hour start time")
    end_time = models.TimeField(help_text="Hour end time")
    line = models.ForeignKey(
        'workflow_config.AssemblyLine',
        on_delete=models.PROTECT,
        related_name='schedules'
    )
    work_order = models.ForeignKey(
        'operation.WorkOrder',
        on_delete=models.PROTECT,
        related_name='schedules'
    )
    shift = models.ForeignKey(
        Shift,
        on_delete=models.PROTECT,
        related_name='schedules'
    )
    planned_break_minutes = models.IntegerField(
        default=0,
        help_text="Planned break duration in minutes",
        validators=[MinValueValidator(0)]
    )
    downtime_minutes = models.IntegerField(
        default=0,
        help_text="Unplanned downtime in minutes",
        validators=[MinValueValidator(0)]
    )
    downtime_reason = models.CharField(
        max_length=200,
        blank=True,
        help_text="Reason for downtime"
    )
    manpower = models.IntegerField(
        help_text="Number of workers",
        validators=[MinValueValidator(1)]
    )
    actual_quantity = models.IntegerField(
        null=True,
        blank=True,
        help_text="Actual production quantity",
        validators=[MinValueValidator(0)]
    )
    rejection_quantity = models.IntegerField(
        default=0,
        help_text="Number of rejected units",
        validators=[MinValueValidator(0)]
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_schedules'
    )

    class Meta:
        db_table = 'mes_production_schedule'
        unique_together = ['production_date', 'start_time', 'line']
        ordering = ['-production_date', 'start_time', 'line__name']

    def __str__(self):
        return f"{self.line.name} - {self.production_date} {self.start_time}-{self.end_time}"

    def clean(self):
        """Custom validation for production schedule"""
        super().clean()

        # Validate time ranges
        if self.start_time and self.end_time and self.start_time >= self.end_time:
            raise ValidationError("Start time must be before end time")

        # Calculate total available minutes
        if self.start_time and self.end_time:
            start_minutes = self.start_time.hour * 60 + self.start_time.minute
            end_minutes = self.end_time.hour * 60 + self.end_time.minute
            total_minutes = end_minutes - start_minutes

            # Validate break and downtime don't exceed total time
            if (self.planned_break_minutes + self.downtime_minutes) > total_minutes:
                raise ValidationError(
                    "Planned breaks and downtime cannot exceed total available time"
                )

        # Validate rejection quantity doesn't exceed actual quantity
        if (self.actual_quantity is not None and
            self.rejection_quantity > self.actual_quantity):
            raise ValidationError(
                "Rejection quantity cannot exceed actual quantity"
            )

        # Validate shift alignment with schedule times
        self._validate_shift_alignment()

    def _validate_shift_alignment(self):
        """
        Validate that the schedule's time range aligns with the selected shift
        Handles shifts that cross midnight and allows ±15 minutes tolerance
        """
        if not (self.shift and self.start_time and self.end_time):
            return

        # Convert times to minutes for easier calculation
        schedule_start_minutes = self.start_time.hour * 60 + self.start_time.minute
        schedule_end_minutes = self.end_time.hour * 60 + self.end_time.minute
        shift_start_minutes = self.shift.start_time.hour * 60 + self.shift.start_time.minute
        shift_end_minutes = self.shift.end_time.hour * 60 + self.shift.end_time.minute

        # Tolerance in minutes (±15 minutes)
        tolerance = 15

        # Check if shift crosses midnight
        shift_crosses_midnight = shift_start_minutes >= shift_end_minutes

        if shift_crosses_midnight:
            # For shifts that cross midnight (e.g., 22:00 - 06:00)
            # Schedule can be in either part of the shift

            # Check if schedule is in the evening part (after shift start)
            evening_valid = (
                schedule_start_minutes >= (shift_start_minutes - tolerance) and
                schedule_end_minutes >= schedule_start_minutes and
                schedule_end_minutes <= (24 * 60)  # Before midnight
            )

            # Check if schedule is in the morning part (before shift end)
            morning_valid = (
                schedule_start_minutes >= 0 and
                schedule_start_minutes <= (shift_end_minutes + tolerance) and
                schedule_end_minutes >= schedule_start_minutes and
                schedule_end_minutes <= (shift_end_minutes + tolerance)
            )

            # Check if schedule spans across midnight (starts evening, ends morning)
            cross_midnight_valid = (
                schedule_start_minutes >= (shift_start_minutes - tolerance) and
                schedule_end_minutes <= (shift_end_minutes + tolerance) and
                schedule_start_minutes > schedule_end_minutes  # Schedule also crosses midnight
            )

            if not (evening_valid or morning_valid or cross_midnight_valid):
                raise ValidationError(
                    f"Schedule time ({self.start_time} - {self.end_time}) does not align "
                    f"with shift '{self.shift.name}' ({self.shift.start_time} - {self.shift.end_time}). "
                    f"Tolerance: ±{tolerance} minutes."
                )
        else:
            # For normal shifts within the same day
            if not (
                schedule_start_minutes >= (shift_start_minutes - tolerance) and
                schedule_end_minutes <= (shift_end_minutes + tolerance) and
                schedule_start_minutes < schedule_end_minutes
            ):
                raise ValidationError(
                    f"Schedule time ({self.start_time} - {self.end_time}) does not align "
                    f"with shift '{self.shift.name}' ({self.shift.start_time} - {self.shift.end_time}). "
                    f"Tolerance: ±{tolerance} minutes."
                )

    @property
    def total_time_minutes(self):
        """Calculate total time in minutes"""
        if not self.start_time or not self.end_time:
            return 0
        start_minutes = self.start_time.hour * 60 + self.start_time.minute
        end_minutes = self.end_time.hour * 60 + self.end_time.minute
        return end_minutes - start_minutes

    @property
    def machine_runtime_minutes(self):
        """Calculate actual machine runtime in minutes"""
        return self.total_time_minutes - self.planned_break_minutes - self.downtime_minutes

    @property
    def actual_ok_quantity(self):
        """Calculate actual OK quantity (Actual - Rejection)"""
        if self.actual_quantity is None:
            return None
        return self.actual_quantity - self.rejection_quantity


class ProductionMetrics(BaseModel):
    """
    Auto-calculated production metrics
    """
    schedule = models.OneToOneField(
        ProductionSchedule,
        on_delete=models.CASCADE,
        related_name='metrics'
    )
    line_capacity = models.ForeignKey(
        LineCapacity,
        on_delete=models.PROTECT,
        related_name='metrics'
    )
    machine_runtime_hours = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        help_text="Actual machine runtime in hours",
        validators=[MinValueValidator(0)]
    )
    target_quantity = models.IntegerField(
        help_text="Target production based on capacity",
        validators=[MinValueValidator(0)]
    )
    actual_ok_quantity = models.IntegerField(
        help_text="Actual quantity minus rejections",
        validators=[MinValueValidator(0)]
    )
    efficiency_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        help_text="(Actual/Target) * 100",
        validators=[MinValueValidator(0), MaxValueValidator(1000)]  # Allow over 100% efficiency
    )
    quality_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        help_text="(OK Qty/Actual) * 100",
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    upph_actual = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        help_text="Actual UPPH achieved",
        validators=[MinValueValidator(0)]
    )
    line_capacity_calculated = models.IntegerField(
        help_text="Calculated line capacity for the period",
        validators=[MinValueValidator(0)]
    )

    class Meta:
        db_table = 'mes_production_metrics'
        ordering = ['-schedule__production_date', 'schedule__start_time']

    def __str__(self):
        return f"Metrics for {self.schedule}"


class DowntimeLog(BaseModel):
    """
    Detailed downtime tracking with reasons and categories
    """
    DOWNTIME_TYPE_CHOICES = [
        ('planned', 'Planned'),
        ('unplanned', 'Unplanned'),
    ]

    schedule = models.ForeignKey(
        ProductionSchedule,
        on_delete=models.CASCADE,
        related_name='downtime_logs'
    )
    downtime_type = models.CharField(
        max_length=20,
        choices=DOWNTIME_TYPE_CHOICES,
        help_text="Type of downtime"
    )
    duration_minutes = models.IntegerField(
        help_text="Duration of downtime in minutes",
        validators=[MinValueValidator(1)]
    )
    reason = models.CharField(
        max_length=100,
        help_text="Reason code for downtime"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of the downtime"
    )
    start_time = models.DateTimeField(help_text="When the downtime started")
    end_time = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the downtime ended"
    )
    logged_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='logged_downtimes'
    )

    class Meta:
        db_table = 'mes_downtime_log'
        ordering = ['-start_time']

    def __str__(self):
        return f"{self.downtime_type} - {self.reason} ({self.duration_minutes}min)"

    def clean(self):
        """Custom validation for downtime log"""
        super().clean()

        # Validate end time is after start time
        if self.start_time and self.end_time and self.end_time <= self.start_time:
            raise ValidationError("End time must be after start time")

        # Validate duration matches start/end times if both are provided
        if self.start_time and self.end_time:
            calculated_duration = (self.end_time - self.start_time).total_seconds() / 60
            if abs(self.duration_minutes - calculated_duration) > 1:  # Allow 1 minute tolerance
                raise ValidationError(
                    f"Duration ({self.duration_minutes} min) doesn't match "
                    f"calculated duration ({calculated_duration:.1f} min)"
                )
